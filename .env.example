# Naroop Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Security Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production
CSRF_SECRET=your-csrf-secret-key-change-this-in-production
JWT_SECRET=your-jwt-secret-key-change-this-in-production

# Database Configuration (for future database integration)
# DATABASE_URL=postgresql://username:password@localhost:5432/naroop
# REDIS_URL=redis://localhost:6379

# External Services
GEMINI_API_KEY=your-gemini-api-key-for-ai-features

# Email Configuration (for future email features)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/naroop.log

# Security Headers
ENABLE_HELMET=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000

# Performance Configuration
ENABLE_COMPRESSION=true
ENABLE_CACHE=true
CACHE_TTL=300

# Monitoring
ENABLE_METRICS=false
METRICS_PORT=9090

# Development Configuration
ENABLE_DEBUG=false
ENABLE_HOT_RELOAD=false
