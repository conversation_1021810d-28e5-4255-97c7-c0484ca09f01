{"name": "naroop-social-platform", "version": "1.0.0", "description": "Naroop - A social media platform where Black people share positive stories and celebrate community", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "node server.js", "prod": "node server.js", "logs": "type logs\\naroop.log", "setup": "npm install && node scripts/setup.js", "health": "node -e \"require('http').get('http://localhost:3000/health', res => { let data = ''; res.on('data', chunk => data += chunk); res.on('end', () => console.log(data)); })\""}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "optionalDependencies": {"dotenv": "^16.3.1", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "keywords": ["social-media", "black-community", "positive-stories", "storytelling", "community", "naroop"], "author": "<PERSON>", "license": "MIT"}